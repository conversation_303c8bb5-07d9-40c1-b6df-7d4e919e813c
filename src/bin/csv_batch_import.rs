use payermax::config::Config;
use payermax::service::BatchImportService;
use sea_orm::Database;
use std::env;
use std::fs;
use std::path::{Path, PathBuf};
use tracing::{error, info, warn};
use tracing_subscriber;

fn get_csv_files(dir: &Path) -> Result<Vec<PathBuf>, Box<dyn std::error::Error>> {
    let mut csv_files = Vec::new();
    
    for entry in fs::read_dir(dir)? {
        let entry = entry?;
        let path = entry.path();
        
        if path.is_file() {
            if let Some(ext) = path.extension() {
                if ext.to_string_lossy().to_lowercase() == "csv" {
                    csv_files.push(path);
                }
            }
        }
    }
    
    // 按文件名排序，确保处理顺序一致
    csv_files.sort();
    Ok(csv_files)
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 获取命令行参数
    let args: Vec<String> = env::args().collect();
    if args.len() < 2 {
        eprintln!(
            "Usage: {} <csv_directory> [max_concurrent_files] [batch_size]",
            args[0]
        );
        eprintln!("Example: {} csv_data 4 1000", args[0]);
        eprintln!("Note: This tool only processes CSV files in the specified directory");
        std::process::exit(1);
    }

    let data_dir = &args[1];
    let max_concurrent_files = args
        .get(2)
        .and_then(|s| s.parse::<usize>().ok())
        .unwrap_or(4); // CSV文件通常比Excel文件处理更快，可以适当增加并发
    let batch_size = args
        .get(3)
        .and_then(|s| s.parse::<usize>().ok())
        .unwrap_or(1000);

    info!("Starting CSV batch import:");
    info!("  CSV directory: {}", data_dir);
    info!("  Max concurrent files: {}", max_concurrent_files);
    info!("  Batch size: {}", batch_size);

    // 检查数据目录是否存在
    let data_path = Path::new(data_dir);
    if !data_path.exists() {
        error!("CSV directory '{}' does not exist", data_dir);
        std::process::exit(1);
    }

    // 检查目录中是否有CSV文件
    let csv_files = get_csv_files(data_path)?;
    if csv_files.is_empty() {
        warn!("No CSV files found in directory: {}", data_dir);
        info!("Please ensure the directory contains .csv files");
        return Ok(());
    }

    info!("Found {} CSV files to process:", csv_files.len());
    for (i, file) in csv_files.iter().enumerate() {
        info!("  {}: {}", i + 1, file.display());
    }

    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env().unwrap_or_else(|e| {
        error!("Failed to load configuration: {:?}", e);
        std::process::exit(1);
    });

    // 连接数据库 - 配置连接池以支持高并发批量导入
    info!("Connecting to database: {}", config.database_url);

    let mut opt = sea_orm::ConnectOptions::new(&config.database_url);
    opt.max_connections(15) // CSV文件处理相对较快，适度减少连接数
        .min_connections(3) // 保持最小连接数
        .connect_timeout(std::time::Duration::from_secs(30)) // 连接超时30秒
        .acquire_timeout(std::time::Duration::from_secs(30)) // 获取连接超时30秒
        .idle_timeout(std::time::Duration::from_secs(300)) // 空闲连接5分钟超时
        .max_lifetime(std::time::Duration::from_secs(3600)) // 连接最大生命周期1小时
        .sqlx_logging(false); // 关闭sqlx详细日志，减少日志噪音

    let db = Database::connect(opt).await?;
    info!("Connected to database with connection pool (max: 15, min: 3)");

    // 创建批量导入服务
    let service = BatchImportService::new(db)
        .with_concurrency(max_concurrent_files)
        .with_batch_size(batch_size);

    // 执行批量导入
    match service.import_data_directory(data_path).await {
        Ok(progress) => {
            info!("CSV batch import completed successfully!");
            info!("📊 Import Statistics:");
            info!("  Total files: {}", progress.total_files);
            info!("  Processed files: {}", progress.processed_files);
            info!("  Total records: {}", progress.processed_records);

            if !progress.failed_files.is_empty() {
                error!("❌ Failed files:");
                for failed_file in &progress.failed_files {
                    error!("  - {}", failed_file);
                }
                
                // 提供重试建议
                info!("💡 To retry failed files, you can:");
                info!("  1. Check the file format and data integrity");
                info!("  2. Use csv_import_single_file to import individual files");
                info!("  3. Re-run this command to skip already imported files");
            } else {
                info!("🎉 All CSV files imported successfully!");
            }
        }
        Err(e) => {
            error!("CSV batch import failed: {:?}", e);
            error!("💡 Troubleshooting tips:");
            error!("  1. Check database connection and permissions");
            error!("  2. Verify CSV file format matches expected schema");
            error!("  3. Ensure sufficient disk space and memory");
            error!("  4. Try importing individual files to identify problematic data");
            std::process::exit(1);
        }
    }

    Ok(())
}
