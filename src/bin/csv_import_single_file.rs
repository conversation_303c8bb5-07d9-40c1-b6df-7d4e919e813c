use payermax::config::Config;
use payermax::service::batch_import_service::BatchImportService;
use payermax::service::file_parser::FileParser;
use sea_orm::{Database, EntityTrait, ColumnTrait, QueryFilter, PaginatorTrait};
use entity::transactions;
use std::env;
use std::path::Path;
use tracing::{error, info, warn};

fn is_csv_file(path: &Path) -> bool {
    if let Some(ext) = path.extension() {
        ext.to_string_lossy().to_lowercase() == "csv"
    } else {
        false
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 获取命令行参数
    let args: Vec<String> = env::args().collect();
    if args.len() < 2 {
        eprintln!("Usage: {} <csv_file_path> [batch_size]", args[0]);
        eprintln!("Example: {} data/transactions.csv 1000", args[0]);
        eprintln!("Note: This tool only processes CSV files (.csv extension)");
        std::process::exit(1);
    }

    let file_path = Path::new(&args[1]);
    let batch_size = args.get(2)
        .and_then(|s| s.parse::<usize>().ok())
        .unwrap_or(1000);
    
    // 验证文件存在性
    if !file_path.exists() {
        error!("File does not exist: {}", file_path.display());
        std::process::exit(1);
    }

    if !file_path.is_file() {
        error!("Path is not a file: {}", file_path.display());
        std::process::exit(1);
    }

    // 验证文件格式
    if !is_csv_file(file_path) {
        error!("File is not a CSV file: {}", file_path.display());
        error!("Please provide a file with .csv extension");
        std::process::exit(1);
    }

    let filename = file_path.file_name()
        .and_then(|name| name.to_str())
        .ok_or("Invalid filename")?;

    info!("Starting CSV single file import:");
    info!("  File: {}", file_path.display());
    info!("  Filename: {}", filename);
    info!("  Batch size: {}", batch_size);

    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    info!("Connecting to database...");
    let db = Database::connect(&config.database_url).await?;
    info!("✅ Connected to database");

    // 解析CSV文件
    info!("📄 Parsing CSV file...");
    let parse_result = match FileParser::parse_file(file_path) {
        Ok(result) => result,
        Err(e) => {
            error!("❌ Failed to parse CSV file: {}", e);
            error!("💡 Common CSV parsing issues:");
            error!("  1. File encoding (try UTF-8)");
            error!("  2. Delimiter issues (ensure comma-separated)");
            error!("  3. Header row format");
            error!("  4. Data type mismatches");
            std::process::exit(1);
        }
    };
    
    let total_csv_rows = parse_result.rows.len();
    info!("📊 CSV file contains {} data rows", total_csv_rows);
    
    if total_csv_rows == 0 {
        warn!("⚠️  CSV file contains no data rows");
        info!("Please check if the file has a header row and data rows");
        return Ok(());
    }

    // 显示CSV文件的列信息
    info!("📋 CSV columns detected: {:?}", parse_result.headers);

    // 检查数据库中已存在的记录数
    info!("🔍 Checking existing records in database...");
    let existing_count = transactions::Entity::find()
        .filter(transactions::Column::Filename.eq(filename))
        .count(&db)
        .await?;
    
    info!("📊 Database contains {} existing records for this file", existing_count);

    if existing_count as usize >= total_csv_rows {
        info!("✅ All records already imported. No action needed.");
        info!("💡 If you want to re-import, consider:");
        info!("  1. Deleting existing records for this file first");
        info!("  2. Using a different filename");
        return Ok(());
    }

    let missing_count = total_csv_rows - existing_count as usize;
    info!("📈 Missing {} records, will use BatchImportService to import", missing_count);

    // 使用BatchImportService的单文件导入功能
    let batch_service = BatchImportService::new(db.clone()).with_batch_size(batch_size);

    info!("🚀 Starting import using BatchImportService...");
    match batch_service.import_single_file(file_path).await {
        Ok(progress) => {
            info!("✅ CSV import completed successfully!");
            info!("📊 Import Statistics:");
            info!("  Total files: {}", progress.total_files);
            info!("  Processed files: {}", progress.processed_files);
            info!("  Total records: {}", progress.total_records);
            info!("  Processed records: {}", progress.processed_records);
            
            if !progress.failed_files.is_empty() {
                warn!("⚠️  Failed files: {:?}", progress.failed_files);
            }
            
            // 验证最终结果
            info!("🔍 Verifying final import results...");
            let final_count = transactions::Entity::find()
                .filter(transactions::Column::Filename.eq(filename))
                .count(&db)
                .await?;
            
            info!("📈 Final database count: {}", final_count);
            
            if final_count as usize == total_csv_rows {
                info!("🎉 SUCCESS: All CSV records are now in the database!");
            } else {
                let still_missing = total_csv_rows - final_count as usize;
                warn!("⚠️  WARNING: {} records are still missing", still_missing);
                warn!("💡 Possible reasons:");
                warn!("  1. Data validation failures");
                warn!("  2. Duplicate records (skipped)");
                warn!("  3. Format conversion issues");
                warn!("  4. Database constraints violations");
            }
        }
        Err(e) => {
            error!("❌ CSV import failed: {}", e);
            error!("💡 Troubleshooting tips:");
            error!("  1. Check CSV file format and encoding");
            error!("  2. Verify database connection and permissions");
            error!("  3. Ensure CSV columns match expected schema");
            error!("  4. Check for data type compatibility");
            error!("  5. Review database logs for detailed error messages");
            std::process::exit(1);
        }
    }

    Ok(())
}
