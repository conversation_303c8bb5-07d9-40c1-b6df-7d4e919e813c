use chrono::Utc;
use entity::import_record::Entity as ImportRecord;
use entity::sum_transactions::{
    ActiveModel as SumTransactionsActiveModel, Entity as SumTransactions,
};
use entity::transactions::Entity as Transactions;
use payermax::config::Config;
use payermax::service::file_parser::FileParser;
use rust_decimal::Decimal;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, Database, EntityTrait, PaginatorTrait, QueryFilter, Set,
};
use std::collections::HashMap;
use std::path::Path;
use tracing::{error, info, warn};

#[derive(Debug)]
struct SummaryData {
    request_time: String,
    account_subject: String,
    account_type: String,
    account_currency: String,
    accounting_amount_in: Decimal,
    accounting_amount_out: Decimal,
    balance_end: Decimal,
    qty_in: i32,
    qty_out: i32,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 获取命令行参数
    let args: Vec<String> = std::env::args().collect();
    if args.len() < 2 {
        eprintln!("用法: {} <filename>", args[0]);
        eprintln!("示例: {} SP20519158_bill_20220701_20230630_0.xlsx", args[0]);
        eprintln!(
            "      {} --force SP20519158_bill_20220701_20230630_0.xlsx  # 强制处理不完整文件",
            args[0]
        );
        std::process::exit(1);
    }

    let force_mode = args.contains(&"--force".to_string());
    let filename = if force_mode {
        args.get(2).unwrap_or_else(|| {
            eprintln!("错误: 使用 --force 时需要指定文件名");
            std::process::exit(1);
        })
    } else {
        &args[1]
    };

    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    info!("Connected to database");

    println!("📊 PayerMax 单文件汇总统计工具");
    println!("==============================");
    println!("目标文件: {}", filename);
    if force_mode {
        println!("模式: 强制处理（忽略完整性检查）");
    }

    // 1. 检查文件是否存在于数据库中
    let db_count = Transactions::find()
        .filter(entity::transactions::Column::Filename.eq(filename))
        .count(&db)
        .await?;

    if db_count == 0 {
        eprintln!("❌ 错误: 文件 '{}' 在数据库中不存在", filename);
        eprintln!("请确认文件名正确，或先导入该文件");
        std::process::exit(1);
    }

    println!("数据库记录数: {}", db_count);

    // 2. 检查文件完整性（除非使用 --force）
    if !force_mode {
        // 检查 import_record
        let import_record = ImportRecord::find()
            .filter(entity::import_record::Column::FileName.eq(filename))
            .one(&db)
            .await?;

        if let Some(record) = import_record {
            let expected_count = record.total_rows.unwrap_or(0) as u64;

            if db_count != expected_count {
                eprintln!("⚠️  警告: 文件不完整");
                eprintln!("   数据库记录数: {}", db_count);
                eprintln!("   预期记录数: {}", expected_count);
                eprintln!("   缺失记录数: {}", expected_count as i64 - db_count as i64);
                eprintln!();
                eprintln!("选项:");
                eprintln!("1. 使用 --force 参数强制处理不完整文件");
                eprintln!("2. 重新导入该文件以补齐缺失数据");
                std::process::exit(1);
            }

            if record.status != "completed" {
                eprintln!("⚠️  警告: 文件状态为 '{}'，不是 'completed'", record.status);
                eprintln!("使用 --force 参数可强制处理");
                if !force_mode {
                    std::process::exit(1);
                }
            }

            println!("✅ 文件完整性检查通过");
        } else {
            // 尝试通过原始文件验证
            let file_path = Path::new("data").join(filename);
            if file_path.exists() {
                match FileParser::parse_file(&file_path) {
                    Ok(parse_result) => {
                        let expected_count = parse_result.rows.len() as u64;
                        if db_count != expected_count {
                            eprintln!("⚠️  警告: 文件不完整（通过原始文件验证）");
                            eprintln!("   数据库记录数: {}", db_count);
                            eprintln!("   文件实际行数: {}", expected_count);
                            eprintln!("使用 --force 参数可强制处理");
                            if !force_mode {
                                std::process::exit(1);
                            }
                        } else {
                            println!("✅ 文件完整性检查通过（通过原始文件验证）");
                        }
                    }
                    Err(e) => {
                        warn!("无法验证原始文件: {}", e);
                        println!("⚠️  无法验证文件完整性，继续处理...");
                    }
                }
            } else {
                warn!("原始文件不存在: {}", filename);
                println!("⚠️  无法验证文件完整性，继续处理...");
            }
        }
    } else {
        println!("⚠️  跳过完整性检查（强制模式）");
    }

    // 3. 检查是否已有汇总数据
    let existing_summaries = SumTransactions::find()
        .filter(entity::sum_transactions::Column::File.eq(filename))
        .count(&db)
        .await?;

    if existing_summaries > 0 {
        println!(
            "📋 发现 {} 条现有汇总记录，将进行智能更新",
            existing_summaries
        );
    } else {
        println!("📋 未发现现有汇总记录，将创建新的汇总");
    }

    // 4. 处理文件汇总
    println!("\n🔄 开始处理汇总统计...");
    match process_file_summary(&db, filename).await {
        Ok((inserted, updated)) => {
            println!("\n🎉 处理完成!");
            println!("文件: {}", filename);
            println!("插入新记录: {} 条", inserted);
            println!("更新记录: {} 条", updated);
            println!("总处理记录: {} 条", inserted + updated);

            if inserted > 0 && updated > 0 {
                println!("💡 提示: 既有新增又有更新，说明数据结构可能发生了变化");
            } else if updated > 0 {
                println!("💡 提示: 全部为更新操作，数据已同步");
            } else if inserted > 0 {
                println!("💡 提示: 全部为新增操作，首次生成汇总");
            }
        }
        Err(e) => {
            error!("❌ 处理失败: {}", e);
            std::process::exit(1);
        }
    }

    Ok(())
}

async fn process_file_summary(
    db: &sea_orm::DatabaseConnection,
    filename: &str,
) -> Result<(usize, usize), Box<dyn std::error::Error>> {
    // 查询该文件的所有交易记录
    let transactions = Transactions::find()
        .filter(entity::transactions::Column::Filename.eq(filename))
        .all(db)
        .await?;

    if transactions.is_empty() {
        return Ok((0, 0));
    }

    info!("查询到 {} 条交易记录", transactions.len());

    // 按 request_time(yyyyMM), account_subject, account_type, account_currency 分组
    let mut groups: HashMap<(String, String, String, String), Vec<&entity::transactions::Model>> =
        HashMap::new();

    for transaction in &transactions {
        // 转换 request_time 为 yyyyMM 格式
        let request_time_yyyymm = format_request_time(&transaction.request_time);

        let key = (
            request_time_yyyymm,
            transaction.account_subject.clone(),
            transaction.accounting_type.clone(),
            transaction.accounting_currency.clone(),
        );

        groups.entry(key).or_insert_with(Vec::new).push(transaction);
    }

    info!("分组完成，共 {} 个分组", groups.len());

    // 为每个分组生成汇总记录
    let mut summaries = Vec::new();

    for ((request_time, account_subject, account_type, account_currency), group_transactions) in
        groups
    {
        let mut accounting_amount_in = Decimal::new(0, 0);
        let mut accounting_amount_out = Decimal::new(0, 0);
        let mut qty_in = 0i32;
        let mut qty_out = 0i32;
        let mut balance_end = Decimal::new(0, 0);

        // 按 request_time 排序，取最后一条记录的 balance 作为 balance_end
        let mut sorted_transactions = group_transactions.clone();
        sorted_transactions.sort_by(|a, b| a.request_time.cmp(&b.request_time));

        if let Some(last_transaction) = sorted_transactions.last() {
            balance_end = last_transaction.balance.unwrap_or(Decimal::new(0, 0));
        }

        // 统计进出金额和数量
        for transaction in &group_transactions {
            if transaction.accounting_amount > Decimal::new(0, 0) {
                accounting_amount_in += transaction.accounting_amount;
                qty_in += 1;
            } else if transaction.accounting_amount < Decimal::new(0, 0) {
                accounting_amount_out += transaction.accounting_amount.abs();
                qty_out += 1;
            }
        }

        let summary = SummaryData {
            request_time,
            account_subject,
            account_type,
            account_currency,
            accounting_amount_in,
            accounting_amount_out,
            balance_end,
            qty_in,
            qty_out,
        };

        summaries.push(summary);
    }

    // 智能插入或更新汇总记录
    let mut inserted_count = 0;
    let mut updated_count = 0;

    for summary in summaries {
        let balance_start =
            summary.balance_end - summary.accounting_amount_in - summary.accounting_amount_out;

        // 检查是否已存在相同的记录
        let existing_record = SumTransactions::find()
            .filter(entity::sum_transactions::Column::File.eq(filename))
            .filter(entity::sum_transactions::Column::RequestTime.eq(&summary.request_time))
            .filter(entity::sum_transactions::Column::AccountSubject.eq(&summary.account_subject))
            .filter(entity::sum_transactions::Column::AccountCurrency.eq(&summary.account_currency))
            .one(db)
            .await?;

        if let Some(existing) = existing_record {
            // 记录已存在，检查是否需要更新
            let needs_update = existing.account_type != summary.account_type
                || existing.balance_start.unwrap_or_default() != balance_start
                || existing.accounting_amount_in.unwrap_or_default()
                    != summary.accounting_amount_in
                || existing.accounting_amount_out.unwrap_or_default()
                    != summary.accounting_amount_out
                || existing.balance_end.unwrap_or_default() != summary.balance_end
                || existing.qty_in.unwrap_or_default() != summary.qty_in
                || existing.qty_out.unwrap_or_default() != summary.qty_out;

            if needs_update {
                // 更新现有记录
                let mut active_model: SumTransactionsActiveModel = existing.into();
                active_model.account_type = Set(summary.account_type.clone());
                active_model.balance_start = Set(Some(balance_start));
                active_model.accounting_amount_in = Set(Some(summary.accounting_amount_in));
                active_model.accounting_amount_out = Set(Some(summary.accounting_amount_out));
                active_model.balance_end = Set(Some(summary.balance_end));
                active_model.qty_in = Set(Some(summary.qty_in));
                active_model.qty_out = Set(Some(summary.qty_out));
                active_model.updated_at = Set(Some(Utc::now().into()));

                match active_model.update(db).await {
                    Ok(_) => {
                        updated_count += 1;
                        info!(
                            "更新: {} - {} - {} - {}",
                            summary.request_time,
                            summary.account_subject,
                            summary.account_type,
                            summary.account_currency
                        );
                    }
                    Err(e) => error!("更新失败: {}", e),
                }
            } else {
                info!(
                    "跳过: {} - {} - {} - {} (无变化)",
                    summary.request_time,
                    summary.account_subject,
                    summary.account_type,
                    summary.account_currency
                );
            }
        } else {
            // 记录不存在，插入新记录
            let active_model = SumTransactionsActiveModel {
                id: sea_orm::NotSet,
                file: Set(filename.to_string()),
                request_time: Set(summary.request_time.clone()),
                account_subject: Set(summary.account_subject.clone()),
                account_type: Set(summary.account_type.clone()),
                account_currency: Set(summary.account_currency.clone()),
                balance_start: Set(Some(balance_start)),
                accounting_amount_in: Set(Some(summary.accounting_amount_in)),
                accounting_amount_out: Set(Some(summary.accounting_amount_out)),
                balance_end: Set(Some(summary.balance_end)),
                qty_in: Set(Some(summary.qty_in)),
                qty_out: Set(Some(summary.qty_out)),
                created_at: Set(Some(Utc::now().into())),
                updated_at: Set(Some(Utc::now().into())),
            };

            match active_model.insert(db).await {
                Ok(_) => {
                    inserted_count += 1;
                    info!(
                        "插入: {} - {} - {} - {}",
                        summary.request_time,
                        summary.account_subject,
                        summary.account_type,
                        summary.account_currency
                    );
                }
                Err(e) => error!("插入失败: {}", e),
            }
        }
    }

    info!(
        "处理完成: 插入 {} 条，更新 {} 条",
        inserted_count, updated_count
    );
    Ok((inserted_count, updated_count))
}

fn format_request_time(request_time: &chrono::DateTime<chrono::FixedOffset>) -> String {
    request_time.format("%Y%m").to_string()
}
