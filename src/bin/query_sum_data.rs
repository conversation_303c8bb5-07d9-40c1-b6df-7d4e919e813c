use csv::Writer;
use entity::sum_transactions::{Column, Entity as SumTransactions};
use payermax::config::Config;
use sea_orm::{ColumnTrait, Database, EntityTrait, QueryFilter, QueryOrder};
use std::env;
use std::fs;
use std::path::{Path, PathBuf};
use tracing::info;

#[derive(Debug)]
struct Args {
    query_type: QueryType,
    output_dir: Option<PathBuf>,
}

#[derive(Debug)]
enum QueryType {
    Full,
    FieldQuery { field: String, value: String },
}

fn parse_args() -> Result<Args, String> {
    let args: Vec<String> = env::args().collect();

    if args.len() < 2 {
        return Err(format!(
            "用法: {} [选项]\n\n选项:\n  -f            全量查询所有数据\n  -q <field=value>  单字段查询，格式: field=value\n  -o <目录>     指定输出目录（可选，默认为当前目录）\n\n支持的查询字段:\n  file, request_time, account_subject, account_type, account_currency\n\n示例:\n  {} -f -o output\n  {} -q file=SP20519158_bill_20220701_20230630_0.xlsx\n  {} -q account_currency=USD -o results",
            args[0], args[0], args[0], args[0]
        ));
    }

    let mut query_type = None;
    let mut output_dir = None;
    let mut i = 1;

    while i < args.len() {
        match args[i].as_str() {
            "-f" => {
                if query_type.is_some() {
                    return Err("不能同时指定 -f 和 -q 参数".to_string());
                }
                query_type = Some(QueryType::Full);
                i += 1;
            }
            "-q" => {
                if query_type.is_some() {
                    return Err("不能同时指定 -f 和 -q 参数".to_string());
                }
                if i + 1 >= args.len() {
                    return Err("-q 参数需要指定查询条件，格式: field=value".to_string());
                }

                let query_str = &args[i + 1];
                let parts: Vec<&str> = query_str.split('=').collect();
                if parts.len() != 2 {
                    return Err("查询条件格式错误，应为: field=value".to_string());
                }

                let field = parts[0].trim().to_string();
                let value = parts[1].trim().to_string();

                // 验证字段名
                match field.as_str() {
                    "file" | "request_time" | "account_subject" | "account_type" | "account_currency" => {},
                    _ => return Err(format!("不支持的查询字段: {}。支持的字段: file, request_time, account_subject, account_type, account_currency", field)),
                }

                query_type = Some(QueryType::FieldQuery { field, value });
                i += 2;
            }
            "-o" => {
                if i + 1 >= args.len() {
                    return Err("-o 参数需要指定输出目录路径".to_string());
                }
                output_dir = Some(PathBuf::from(&args[i + 1]));
                i += 2;
            }
            _ => {
                return Err(format!("未知参数: {}", args[i]));
            }
        }
    }

    let query_type = query_type.ok_or("必须指定 -f 或 -q 参数")?;

    Ok(Args {
        query_type,
        output_dir,
    })
}

async fn execute_query(
    db: &sea_orm::DatabaseConnection,
    query_type: &QueryType,
) -> Result<Vec<entity::sum_transactions::Model>, Box<dyn std::error::Error>> {
    match query_type {
        QueryType::Full => {
            info!("执行全量查询");
            let results = SumTransactions::find()
                .order_by_asc(Column::Id)
                .all(db)
                .await?;
            Ok(results)
        }
        QueryType::FieldQuery { field, value } => {
            info!("执行字段查询: {} = {}", field, value);

            let query = match field.as_str() {
                "file" => SumTransactions::find().filter(Column::File.eq(value)),
                "request_time" => SumTransactions::find().filter(Column::RequestTime.eq(value)),
                "account_subject" => {
                    SumTransactions::find().filter(Column::AccountSubject.eq(value))
                }
                "account_type" => SumTransactions::find().filter(Column::AccountType.eq(value)),
                "account_currency" => {
                    SumTransactions::find().filter(Column::AccountCurrency.eq(value))
                }
                _ => return Err(format!("不支持的查询字段: {}", field).into()),
            };

            let results = query.order_by_asc(Column::Id).all(db).await?;
            Ok(results)
        }
    }
}

fn write_results_to_csv(
    results: &[entity::sum_transactions::Model],
    output_path: &Path,
) -> Result<(), Box<dyn std::error::Error>> {
    let mut writer = Writer::from_path(output_path)?;

    // 写入CSV头部
    writer.write_record(&[
        "id",
        "file",
        "request_time",
        "account_subject",
        "account_type",
        "account_currency",
        "balance_start",
        "accounting_amount_in",
        "accounting_amount_out",
        "balance_end",
        "qty_in",
        "qty_out",
        "created_at",
        "updated_at",
    ])?;

    // 写入数据行
    for record in results {
        writer.write_record(&[
            record.id.to_string(),
            record.file.clone(),
            record.request_time.clone(),
            record.account_subject.clone(),
            record.account_type.clone(),
            record.account_currency.clone(),
            record
                .balance_start
                .map_or("".to_string(), |v| v.to_string()),
            record
                .accounting_amount_in
                .map_or("".to_string(), |v| v.to_string()),
            record
                .accounting_amount_out
                .map_or("".to_string(), |v| v.to_string()),
            record.balance_end.map_or("".to_string(), |v| v.to_string()),
            record.qty_in.map_or("".to_string(), |v| v.to_string()),
            record.qty_out.map_or("".to_string(), |v| v.to_string()),
            record.created_at.map_or("".to_string(), |v| v.to_string()),
            record.updated_at.map_or("".to_string(), |v| v.to_string()),
        ])?;
    }

    writer.flush()?;
    Ok(())
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 解析命令行参数
    let args = match parse_args() {
        Ok(args) => args,
        Err(err) => {
            eprintln!("错误: {}", err);
            std::process::exit(1);
        }
    };

    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    info!("✅ 数据库连接成功");

    // 执行查询
    let results = execute_query(&db, &args.query_type).await?;
    info!("查询完成，找到 {} 条记录", results.len());

    if results.is_empty() {
        info!("没有找到匹配的记录");
        return Ok(());
    }

    // 确定输出目录和文件名
    let output_dir = match &args.output_dir {
        Some(dir) => {
            fs::create_dir_all(dir)?;
            dir.clone()
        }
        None => env::current_dir()?,
    };

    let filename = match &args.query_type {
        QueryType::Full => "sum_transactions_full.csv".to_string(),
        QueryType::FieldQuery { field, value } => {
            format!(
                "sum_transactions_{}_{}.csv",
                field,
                value.replace("/", "_").replace("\\", "_")
            )
        }
    };

    let output_path = output_dir.join(filename);

    // 写入CSV文件
    write_results_to_csv(&results, &output_path)?;

    info!("✅ 查询结果已保存到: {}", output_path.display());
    info!("📊 总计 {} 条记录", results.len());

    Ok(())
}
