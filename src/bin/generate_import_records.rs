use chrono::Utc;
use entity::import_record::{ActiveModel as ImportRecordActiveModel, Entity as ImportRecord};
use entity::transactions::Entity as Transactions;
use payermax::config::Config;
use payermax::service::file_parser::FileParser;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, Database, EntityTrait, PaginatorTrait, QueryFilter, Set,
};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use uuid::Uuid;

#[derive(Debug)]
struct FileInfo {
    filename: String,
    file_path: PathBuf,
    db_count: i64,
    excel_count: Option<usize>,
}

#[derive(Debug)]
struct ProcessingStats {
    total_files: usize,
    existing_records: usize,
    created_records: usize,
    failed_records: usize,
    skipped_files: usize,
}

impl ProcessingStats {
    fn new() -> Self {
        Self {
            total_files: 0,
            existing_records: 0,
            created_records: 0,
            failed_records: 0,
            skipped_files: 0,
        }
    }

    fn print_summary(&self) {
        println!("\n=== 处理结果统计 ===");
        println!("📊 总文件数: {}", self.total_files);
        println!("✅ 新创建记录: {}", self.created_records);
        println!("📋 已存在记录: {}", self.existing_records);
        println!("❌ 创建失败: {}", self.failed_records);
        println!("⏭️  跳过文件: {}", self.skipped_files);
        
        if self.created_records > 0 {
            println!("🎉 成功为 {} 个文件创建了 import_record 记录！", self.created_records);
        }
    }
}

async fn scan_directory_files(
    directory: &Path,
    db: &sea_orm::DatabaseConnection,
) -> Result<Vec<FileInfo>, Box<dyn std::error::Error>> {
    println!("🔍 扫描目录: {}", directory.display());
    
    if !directory.exists() {
        return Err(format!("目录不存在: {}", directory.display()).into());
    }

    let mut file_infos = Vec::new();
    
    // 读取目录中的所有Excel文件
    let entries = std::fs::read_dir(directory)?;
    let mut excel_files = Vec::new();
    
    for entry in entries {
        let entry = entry?;
        let path = entry.path();
        
        if path.is_file() {
            if let Some(extension) = path.extension() {
                if let Some(ext_str) = extension.to_str() {
                    if matches!(ext_str.to_lowercase().as_str(), "xlsx" | "xls" | "csv") {
                        if let Some(filename) = path.file_name().and_then(|n| n.to_str()) {
                            excel_files.push((filename.to_string(), path));
                        }
                    }
                }
            }
        }
    }

    println!("📁 发现 {} 个Excel/CSV文件", excel_files.len());

    // 查询数据库中每个文件的记录数
    for (filename, file_path) in excel_files {
        let db_count = Transactions::find()
            .filter(entity::transactions::Column::Filename.eq(&filename))
            .count(db)
            .await?;

        if db_count > 0 {
            file_infos.push(FileInfo {
                filename,
                file_path,
                db_count: db_count as i64,
                excel_count: None,
            });
        } else {
            println!("⚠️  跳过文件 {} (数据库中无记录)", filename);
        }
    }

    println!("✅ 找到 {} 个有数据库记录的文件", file_infos.len());
    Ok(file_infos)
}

async fn process_file(
    file_info: &mut FileInfo,
    db: &sea_orm::DatabaseConnection,
    force_update: bool,
    stats: &mut ProcessingStats,
) -> Result<(), Box<dyn std::error::Error>> {
    let filename = &file_info.filename;
    
    // 检查是否已存在 import_record
    let existing = ImportRecord::find()
        .filter(entity::import_record::Column::FileName.eq(filename))
        .one(db)
        .await?;

    if existing.is_some() && !force_update {
        println!("📋 {} - 已存在记录，跳过", filename);
        stats.existing_records += 1;
        return Ok(());
    }

    if existing.is_some() && force_update {
        println!("🔄 {} - 强制更新模式，将覆盖现有记录", filename);
    }

    // 获取Excel文件行数
    println!("🔍 {} - 正在获取Excel行数...", filename);
    let start_time = std::time::Instant::now();
    
    match FileParser::get_row_count_fast(&file_info.file_path) {
        Ok(excel_count) => {
            let duration = start_time.elapsed();
            println!("📄 {} - Excel行数: {} (耗时: {:?})", filename, excel_count, duration);
            file_info.excel_count = Some(excel_count);
            
            // 对比数据
            let excel_count_i64 = excel_count as i64;
            if excel_count_i64 != file_info.db_count {
                let diff = excel_count_i64 - file_info.db_count;
                println!("⚠️  {} - 数据差异: Excel{}行, 数据库{}行, 差异{}行", 
                    filename, excel_count, file_info.db_count, diff);
            } else {
                println!("✅ {} - 数据一致", filename);
            }
        }
        Err(e) => {
            println!("❌ {} - 获取Excel行数失败: {}", filename, e);
            // 即使获取Excel行数失败，也可以基于数据库记录创建import_record
        }
    }

    // 创建或更新 import_record
    create_or_update_import_record(file_info, db, force_update, stats).await?;
    
    Ok(())
}

async fn create_or_update_import_record(
    file_info: &FileInfo,
    db: &sea_orm::DatabaseConnection,
    force_update: bool,
    stats: &mut ProcessingStats,
) -> Result<(), Box<dyn std::error::Error>> {
    let filename = &file_info.filename;
    let file_path = &file_info.file_path;
    
    // 如果是强制更新模式，先删除现有记录
    if force_update {
        let deleted = ImportRecord::delete_many()
            .filter(entity::import_record::Column::FileName.eq(filename))
            .exec(db)
            .await?;
        
        if deleted.rows_affected > 0 {
            println!("🗑️  {} - 删除了 {} 条现有记录", filename, deleted.rows_affected);
        }
    }

    // 获取文件信息
    let file_size = if file_path.exists() {
        std::fs::metadata(file_path)?.len() as i64
    } else {
        0
    };

    let file_extension = file_path
        .extension()
        .and_then(|ext| ext.to_str())
        .unwrap_or("unknown");

    // 确定状态和行数
    let (status, total_rows, processed_rows, error_rows) = match file_info.excel_count {
        Some(excel_count) => {
            let excel_count_i64 = excel_count as i64;
            if excel_count_i64 == file_info.db_count {
                ("completed".to_string(), Some(file_info.db_count as i32), Some(file_info.db_count as i32), Some(0))
            } else {
                let missing = excel_count_i64 - file_info.db_count;
                if missing > 0 {
                    ("partial".to_string(), Some(excel_count as i32), Some(file_info.db_count as i32), Some(missing as i32))
                } else {
                    ("completed".to_string(), Some(file_info.db_count as i32), Some(file_info.db_count as i32), Some(0))
                }
            }
        }
        None => {
            // Excel行数获取失败，基于数据库记录创建
            ("completed".to_string(), Some(file_info.db_count as i32), Some(file_info.db_count as i32), Some(0))
        }
    };

    // 创建 import_record
    let import_record = ImportRecordActiveModel {
        id: Set(Uuid::new_v4()),
        file_name: Set(filename.clone()),
        file_type: Set(file_extension.to_string()),
        file_size: Set(file_size),
        status: Set(status),
        total_rows: Set(total_rows),
        processed_rows: Set(processed_rows),
        error_rows: Set(error_rows),
        error_message: Set(None),
        created_at: Set(Utc::now().into()),
        updated_at: Set(Utc::now().into()),
    };

    match import_record.insert(db).await {
        Ok(_) => {
            println!("✅ {} - 成功创建import_record", filename);
            stats.created_records += 1;
        }
        Err(e) => {
            println!("❌ {} - 创建import_record失败: {}", filename, e);
            stats.failed_records += 1;
        }
    }

    Ok(())
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = std::env::args().collect();
    
    // 解析命令行参数
    let mut directory = "data".to_string();
    let mut force_update = false;
    let mut update_mode = false;
    
    let mut i = 1;
    while i < args.len() {
        match args[i].as_str() {
            "--force" | "-f" => {
                force_update = true;
                println!("🔄 启用强制更新模式");
            }
            "--update" | "-u" => {
                update_mode = true;
                println!("📝 启用更新模式");
            }
            "--help" | "-h" => {
                println!("用法: cargo run --bin generate_import_records [选项] [目录]");
                println!();
                println!("选项:");
                println!("  --force, -f     强制更新所有记录（删除现有记录后重新创建）");
                println!("  --update, -u    更新模式（跳过已存在的记录）");
                println!("  --help, -h      显示此帮助信息");
                println!();
                println!("参数:");
                println!("  目录            要扫描的文件夹路径（默认: data）");
                println!();
                println!("示例:");
                println!("  cargo run --bin generate_import_records");
                println!("  cargo run --bin generate_import_records --force data");
                println!("  cargo run --bin generate_import_records --update /path/to/files");
                return Ok(());
            }
            arg if !arg.starts_with("--") => {
                directory = arg.to_string();
            }
            _ => {
                println!("⚠️  未知参数: {}", args[i]);
            }
        }
        i += 1;
    }

    // 如果既没有指定force也没有指定update，默认为update模式
    if !force_update && !update_mode {
        update_mode = true;
        println!("📝 默认使用更新模式（跳过已存在记录）");
    }

    println!("=== Import Record 生成工具 ===");
    println!("📁 扫描目录: {}", directory);
    println!("🔧 模式: {}", if force_update { "强制更新" } else { "更新模式" });

    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    println!("✅ 数据库连接成功");

    let directory_path = Path::new(&directory);
    
    // 扫描目录文件
    let mut file_infos = scan_directory_files(directory_path, &db).await?;
    let mut stats = ProcessingStats::new();
    stats.total_files = file_infos.len();

    if file_infos.is_empty() {
        println!("⚠️  没有找到需要处理的文件");
        return Ok(());
    }

    println!("\n=== 开始处理文件 ===");
    
    // 处理每个文件
    for file_info in &mut file_infos {
        match process_file(file_info, &db, force_update, &mut stats).await {
            Ok(_) => {}
            Err(e) => {
                println!("❌ 处理文件 {} 时出错: {}", file_info.filename, e);
                stats.failed_records += 1;
            }
        }
        println!(); // 空行分隔
    }

    // 打印统计结果
    stats.print_summary();

    Ok(())
}
