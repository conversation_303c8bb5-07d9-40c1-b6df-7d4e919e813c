use calamine::{open_workbook, Reader, Xlsx};
use csv::Writer;
use std::env;
use std::fs;
use std::path::{Path, PathBuf};
use tracing::{error, info, warn};

#[derive(Debug)]
struct Args {
    input_type: InputType,
    output_dir: Option<PathBuf>,
}

#[derive(Debug)]
enum InputType {
    Directory(PathBuf),
    File(PathBuf),
}

fn parse_args() -> Result<Args, String> {
    let args: Vec<String> = env::args().collect();
    
    if args.len() < 2 {
        return Err(format!(
            "用法: {} [选项]\n\n选项:\n  -d <目录>     指定输入目录，批量转换所有Excel文件\n  -f <文件>     指定单个Excel文件进行转换\n  -o <目录>     指定输出目录（可选）\n\n示例:\n  {} -d data -o output\n  {} -f data/file.xlsx -o output\n  {} -d data",
            args[0], args[0], args[0], args[0]
        ));
    }

    let mut input_type = None;
    let mut output_dir = None;
    let mut i = 1;

    while i < args.len() {
        match args[i].as_str() {
            "-d" => {
                if i + 1 >= args.len() {
                    return Err("-d 参数需要指定目录路径".to_string());
                }
                let dir_path = PathBuf::from(&args[i + 1]);
                if !dir_path.exists() || !dir_path.is_dir() {
                    return Err(format!("目录不存在或不是有效目录: {}", dir_path.display()));
                }
                input_type = Some(InputType::Directory(dir_path));
                i += 2;
            }
            "-f" => {
                if i + 1 >= args.len() {
                    return Err("-f 参数需要指定文件路径".to_string());
                }
                let file_path = PathBuf::from(&args[i + 1]);
                if !file_path.exists() || !file_path.is_file() {
                    return Err(format!("文件不存在或不是有效文件: {}", file_path.display()));
                }
                input_type = Some(InputType::File(file_path));
                i += 2;
            }
            "-o" => {
                if i + 1 >= args.len() {
                    return Err("-o 参数需要指定输出目录路径".to_string());
                }
                output_dir = Some(PathBuf::from(&args[i + 1]));
                i += 2;
            }
            _ => {
                return Err(format!("未知参数: {}", args[i]));
            }
        }
    }

    let input_type = input_type.ok_or("必须指定 -d 或 -f 参数")?;

    Ok(Args {
        input_type,
        output_dir,
    })
}

fn is_excel_file(path: &Path) -> bool {
    if let Some(ext) = path.extension() {
        let ext = ext.to_string_lossy().to_lowercase();
        ext == "xlsx" || ext == "xls"
    } else {
        false
    }
}

fn get_excel_files(dir: &Path) -> Result<Vec<PathBuf>, Box<dyn std::error::Error>> {
    let mut excel_files = Vec::new();
    
    for entry in fs::read_dir(dir)? {
        let entry = entry?;
        let path = entry.path();
        
        if path.is_file() && is_excel_file(&path) {
            excel_files.push(path);
        }
    }
    
    excel_files.sort();
    Ok(excel_files)
}

fn convert_excel_to_csv(
    excel_path: &Path,
    output_dir: &Path,
) -> Result<(), Box<dyn std::error::Error>> {
    info!("开始转换: {}", excel_path.display());
    
    // 打开Excel文件
    let mut workbook: Xlsx<_> = open_workbook(excel_path)?;
    let sheet_names = workbook.sheet_names().to_owned();
    
    if sheet_names.is_empty() {
        warn!("Excel文件没有工作表: {}", excel_path.display());
        return Ok(());
    }
    
    // 获取文件名（不含扩展名）
    let file_stem = excel_path
        .file_stem()
        .ok_or("无法获取文件名")?
        .to_string_lossy();
    
    // 处理每个工作表
    for (sheet_index, sheet_name) in sheet_names.iter().enumerate() {
        info!("处理工作表: {}", sheet_name);
        
        if let Ok(range) = workbook.worksheet_range(sheet_name) {
            // 生成CSV文件名
            let csv_filename = if sheet_names.len() == 1 {
                format!("{}.csv", file_stem)
            } else {
                format!("{}_{}.csv", file_stem, sheet_index + 1)
            };
            
            let csv_path = output_dir.join(csv_filename);
            
            // 创建CSV写入器
            let mut writer = Writer::from_path(&csv_path)?;
            
            // 写入数据
            let mut row_count = 0;
            for row in range.rows() {
                let string_row: Vec<String> = row.iter()
                    .map(|cell| cell.to_string())
                    .collect();
                writer.write_record(&string_row)?;
                row_count += 1;
            }
            
            writer.flush()?;
            info!("✅ 转换完成: {} -> {} ({} 行)", 
                  excel_path.display(), csv_path.display(), row_count);
        } else {
            error!("无法读取工作表: {}", sheet_name);
        }
    }
    
    Ok(())
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    // 解析命令行参数
    let args = match parse_args() {
        Ok(args) => args,
        Err(err) => {
            eprintln!("错误: {}", err);
            std::process::exit(1);
        }
    };
    
    // 确定输出目录
    let output_dir = match &args.output_dir {
        Some(dir) => {
            // 创建输出目录（如果不存在）
            fs::create_dir_all(dir)?;
            dir.clone()
        }
        None => {
            // 使用当前目录
            env::current_dir()?
        }
    };
    
    info!("输出目录: {}", output_dir.display());
    
    // 根据输入类型处理
    match args.input_type {
        InputType::Directory(dir) => {
            info!("批量转换目录: {}", dir.display());
            let excel_files = get_excel_files(&dir)?;
            
            if excel_files.is_empty() {
                warn!("目录中没有找到Excel文件: {}", dir.display());
                return Ok(());
            }
            
            info!("找到 {} 个Excel文件", excel_files.len());
            
            let mut success_count = 0;
            let mut error_count = 0;
            
            for excel_file in excel_files {
                match convert_excel_to_csv(&excel_file, &output_dir) {
                    Ok(()) => success_count += 1,
                    Err(e) => {
                        error!("转换失败 {}: {}", excel_file.display(), e);
                        error_count += 1;
                    }
                }
            }
            
            info!("批量转换完成: 成功 {} 个，失败 {} 个", success_count, error_count);
        }
        InputType::File(file) => {
            info!("转换单个文件: {}", file.display());
            convert_excel_to_csv(&file, &output_dir)?;
            info!("文件转换完成");
        }
    }
    
    Ok(())
}
