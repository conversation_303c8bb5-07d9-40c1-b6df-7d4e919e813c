use payermax::service::file_parser::FileParser;
use std::path::Path;
use std::time::Instant;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = std::env::args().collect();
    if args.len() < 2 {
        println!("用法: cargo run --bin performance_test <excel文件名>");
        println!("例如: cargo run --bin performance_test data/example.xlsx");
        return Ok(());
    }

    let file_path = Path::new(&args[1]);

    if !file_path.exists() {
        println!("❌ 文件不存在: {}", file_path.display());
        return Ok(());
    }

    // 获取文件大小
    let file_size = std::fs::metadata(file_path)?.len();
    println!("📁 文件: {}", file_path.display());
    println!(
        "📏 文件大小: {:.2} MB",
        file_size as f64 / (1024.0 * 1024.0)
    );

    println!("\n=== 性能测试对比 ===");

    // 测试1: 快速行数统计
    println!("\n🚀 测试1: 快速行数统计 (get_row_count_fast)");
    let start = Instant::now();
    match FileParser::get_row_count_fast(file_path) {
        Ok(row_count) => {
            let duration = start.elapsed();
            println!("✅ 行数: {}", row_count);
            println!("⏱️  耗时: {:?}", duration);
            println!(
                "🏃 速度: {:.2} 行/秒",
                row_count as f64 / duration.as_secs_f64()
            );
        }
        Err(e) => {
            let duration = start.elapsed();
            println!("❌ 失败: {}", e);
            println!("⏱️  耗时: {:?}", duration);
        }
    }

    // 测试1.5: 流式行数统计
    println!("\n⚡ 测试1.5: 流式行数统计 (get_row_count_streaming)");
    let start = Instant::now();
    match FileParser::get_row_count_streaming(file_path) {
        Ok(row_count) => {
            let duration = start.elapsed();
            println!("✅ 行数: {}", row_count);
            println!("⏱️  耗时: {:?}", duration);
            println!(
                "🏃 速度: {:.2} 行/秒",
                row_count as f64 / duration.as_secs_f64()
            );
        }
        Err(e) => {
            let duration = start.elapsed();
            println!("❌ 失败: {}", e);
            println!("⏱️  耗时: {:?}", duration);
        }
    }

    // 测试2: 完整解析
    println!("\n🐌 测试2: 完整解析 (parse_file)");
    let start = Instant::now();
    match FileParser::parse_file(file_path) {
        Ok(parse_result) => {
            let duration = start.elapsed();
            println!("✅ 行数: {}", parse_result.rows.len());
            println!("📋 列数: {}", parse_result.headers.len());
            println!("⏱️  耗时: {:?}", duration);
            println!(
                "🏃 速度: {:.2} 行/秒",
                parse_result.rows.len() as f64 / duration.as_secs_f64()
            );
        }
        Err(e) => {
            let duration = start.elapsed();
            println!("❌ 失败: {}", e);
            println!("⏱️  耗时: {:?}", duration);
        }
    }

    // 多次测试快速统计的稳定性
    println!("\n📊 快速统计稳定性测试 (5次):");
    let mut times = Vec::new();
    for i in 1..=5 {
        let start = Instant::now();
        match FileParser::get_row_count_fast(file_path) {
            Ok(row_count) => {
                let duration = start.elapsed();
                times.push(duration);
                println!("  第{}次: {} 行, 耗时: {:?}", i, row_count, duration);
            }
            Err(e) => {
                println!("  第{}次: 失败 - {}", i, e);
            }
        }
    }

    if !times.is_empty() {
        let avg_time = times.iter().sum::<std::time::Duration>() / times.len() as u32;
        let min_time = times.iter().min().unwrap();
        let max_time = times.iter().max().unwrap();

        println!("\n📈 统计结果:");
        println!("  平均耗时: {:?}", avg_time);
        println!("  最短耗时: {:?}", min_time);
        println!("  最长耗时: {:?}", max_time);
    }

    Ok(())
}
