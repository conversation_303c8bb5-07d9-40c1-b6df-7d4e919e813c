use entity::transactions::Entity as Transactions;
use payermax::config::Config;
use payermax::service::file_parser::FileParser;
use sea_orm::{ColumnTrait, Database, EntityTrait, PaginatorTrait, QueryFilter, QuerySelect};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;
use tokio::task::JoinSet;

#[derive(Serialize, Deserialize, Debug, Clone)]
struct FileCache {
    filename: String,
    file_size: u64,
    row_count: usize,
    last_modified: i64,
}

#[derive(Serialize, Deserialize, Debug, Default, Clone)]
struct CacheData {
    files: HashMap<String, FileCache>,
}

impl CacheData {
    fn load() -> Self {
        if let Ok(content) = std::fs::read_to_string("file_cache.json") {
            serde_json::from_str(&content).unwrap_or_default()
        } else {
            Self::default()
        }
    }

    fn save(&self) {
        if let Ok(content) = serde_json::to_string_pretty(self) {
            let _ = std::fs::write("file_cache.json", content);
        }
    }

    fn get_cached_row_count(&self, filename: &str, file_path: &Path) -> Option<usize> {
        if let Some(cache) = self.files.get(filename) {
            if let Ok(metadata) = std::fs::metadata(file_path) {
                let current_size = metadata.len();
                let current_modified = metadata
                    .modified()
                    .ok()?
                    .duration_since(std::time::UNIX_EPOCH)
                    .ok()?
                    .as_secs() as i64;

                if cache.file_size == current_size && cache.last_modified == current_modified {
                    return Some(cache.row_count);
                }
            }
        }
        None
    }

    fn update_cache(&mut self, filename: String, file_path: &Path, row_count: usize) {
        if let Ok(metadata) = std::fs::metadata(file_path) {
            let file_size = metadata.len();
            let last_modified = metadata
                .modified()
                .unwrap_or(std::time::UNIX_EPOCH)
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs() as i64;

            self.files.insert(
                filename.clone(),
                FileCache {
                    filename,
                    file_size,
                    row_count,
                    last_modified,
                },
            );
        }
    }
}

async fn get_file_row_count_cached(
    filename: &str,
    cache: &mut CacheData,
) -> Result<usize, Box<dyn std::error::Error + Send + Sync>> {
    let file_path = Path::new("data").join(filename);

    if !file_path.exists() {
        return Err(format!("文件不存在: {}", file_path.display()).into());
    }

    // 尝试从缓存获取
    if let Some(cached_count) = cache.get_cached_row_count(filename, &file_path) {
        println!("📋 {} - 使用缓存: {} 行", filename, cached_count);
        return Ok(cached_count);
    }

    // 缓存未命中，解析文件
    println!("🔍 {} - 解析中...", filename);
    let start = std::time::Instant::now();

    match FileParser::get_row_count_fast(&file_path) {
        Ok(row_count) => {
            let duration = start.elapsed();
            println!("✅ {} - {} 行 (耗时: {:?})", filename, row_count, duration);

            // 更新缓存
            cache.update_cache(filename.to_string(), &file_path, row_count);
            Ok(row_count)
        }
        Err(e) => {
            let duration = start.elapsed();
            println!("❌ {} - 解析失败: {} (耗时: {:?})", filename, e, duration);
            Err(e.into())
        }
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 检查命令行参数
    let args: Vec<String> = std::env::args().collect();
    let specific_file = if args.len() > 1 {
        Some(args[1].clone())
    } else {
        None
    };

    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    println!("Connected to database");

    // 加载缓存
    let mut cache = CacheData::load();
    println!("📋 加载缓存: {} 个文件", cache.files.len());

    // 如果指定了特定文件，只分析该文件
    if let Some(filename) = specific_file {
        println!("🎯 专门分析文件: {}", filename);

        // 查询该文件的记录数
        let db_count = Transactions::find()
            .filter(entity::transactions::Column::Filename.eq(&filename))
            .count(&db)
            .await?;

        if db_count == 0 {
            println!("❌ 数据库中没有找到文件: {}", filename);
            return Ok(());
        }

        // 快速获取Excel行数
        match get_file_row_count_cached(&filename, &mut cache).await {
            Ok(excel_rows) => {
                let excel_rows = excel_rows as i64;
                let db_count = db_count as i64;
                if excel_rows != db_count {
                    let diff = excel_rows - db_count;
                    println!(
                        "\n❌ 发现差异: Excel有{}行，数据库有{}行，差异{}行",
                        excel_rows, db_count, diff
                    );
                } else {
                    println!("✅ 数据完整: Excel行数与数据库记录数一致");
                }
            }
            Err(e) => {
                println!("❌ 获取Excel行数失败: {}", e);
            }
        }

        // 保存缓存
        cache.save();
        return Ok(());
    }

    // 查询每个文件的记录数
    let results = Transactions::find()
        .select_only()
        .column_as(entity::transactions::Column::Filename, "filename")
        .column_as(entity::transactions::Column::Id.count(), "count")
        .group_by(entity::transactions::Column::Filename)
        .into_tuple::<(String, i64)>()
        .all(&db)
        .await?;

    println!("\n=== 快速导入结果统计与Excel对比 ===");
    println!("📊 总文件数: {}", results.len());

    let mut total_records = 0i64;
    let mut total_excel_rows = 0i64;
    let mut files_with_differences = 0;

    // 并行处理文件（限制并发数）
    let max_concurrent = 4; // 同时处理4个文件
    let mut join_set = JoinSet::new();
    let mut pending_files = results.clone();
    let mut completed = 0;

    while completed < results.len() {
        // 启动新任务直到达到并发限制
        while join_set.len() < max_concurrent && !pending_files.is_empty() {
            let (filename, count) = pending_files.remove(0);
            let mut cache_clone = cache.clone();

            join_set.spawn(async move {
                let result = get_file_row_count_cached(&filename, &mut cache_clone).await;
                (filename, count, result, cache_clone)
            });
        }

        // 等待一个任务完成
        if let Some(result) = join_set.join_next().await {
            match result {
                Ok((filename, count, excel_result, updated_cache)) => {
                    completed += 1;
                    total_records += count;

                    // 合并缓存更新
                    for (key, value) in updated_cache.files {
                        cache.files.insert(key, value);
                    }

                    match excel_result {
                        Ok(excel_rows) => {
                            let excel_rows = excel_rows as i64;
                            total_excel_rows += excel_rows;

                            if excel_rows != count {
                                let diff = excel_rows - count;
                                println!(
                                    "📁 {} - 数据库: {}, Excel: {} (差异: {})",
                                    filename, count, excel_rows, diff
                                );
                                files_with_differences += 1;
                            } else {
                                println!(
                                    "📁 {} - 数据库: {}, Excel: {} ✅",
                                    filename, count, excel_rows
                                );
                            }
                        }
                        Err(e) => {
                            println!(
                                "📁 {} - 数据库: {}, Excel: 解析失败 - {}",
                                filename, count, e
                            );
                        }
                    }

                    println!("进度: {}/{}", completed, results.len());
                }
                Err(e) => {
                    println!("❌ 任务执行失败: {}", e);
                    completed += 1;
                }
            }
        }
    }

    println!("\n📊 总体统计:");
    println!("  数据库总记录数: {}", total_records);
    println!("  Excel总数据行数: {}", total_excel_rows);
    println!("  总文件数: {}", results.len());
    println!("  有差异的文件数: {}", files_with_differences);

    if total_excel_rows != total_records {
        let total_diff = total_excel_rows - total_records;
        println!("  📈 总体差异: {} 行", total_diff);
        if total_diff > 0 {
            println!("  ⚠️  总共有 {} 行数据未成功导入", total_diff);
        }
    } else {
        println!("  ✅ 所有文件数据完整匹配");
    }

    // 保存缓存
    cache.save();
    println!("\n💾 缓存已保存到 file_cache.json");

    Ok(())
}
