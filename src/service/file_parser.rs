use crate::error::{AppError, AppResult};
use calamine::{open_workbook, Data, Reader, Xlsx};
use chrono::{DateTime, NaiveDateTime, Utc};
use csv::ReaderBuilder;
use serde_json::Value;
use std::collections::HashMap;
use std::path::Path;

#[derive(Debug, Clone)]
pub struct ParsedRow {
    pub row_number: usize,
    pub data: HashMap<String, Value>,
}

#[derive(Debug)]
pub struct ParseResult {
    pub rows: Vec<ParsedRow>,
    pub total_rows: usize,
    pub headers: Vec<String>,
}

#[derive(Debug, Clone)]
pub struct TransactionData {
    pub request_time: Option<DateTime<Utc>>,
    pub accounting_amount: Option<rust_decimal::Decimal>,
    pub balance: Option<rust_decimal::Decimal>,
    pub accounting_currency: Option<String>,
    pub accounting_type: Option<String>,
    pub account_subject: Option<String>,
    pub txn_create_time: Option<DateTime<Utc>>,
    pub txn_complete_time: Option<DateTime<Utc>>,
    pub merchant_txn_id: Option<String>,
    pub trade_order_id: Option<String>,
    pub country: Option<String>,
    pub txn_amount: Option<rust_decimal::Decimal>,
    pub txn_currency: Option<String>,
    pub payee_txn_fee: Option<rust_decimal::Decimal>,
    pub payee_txn_fee_currency: Option<String>,
    pub payer_txn_fee: Option<rust_decimal::Decimal>,
    pub payer_txn_fee_currency: Option<String>,
    pub payee_tax: Option<rust_decimal::Decimal>,
    pub payee_tax_currency: Option<String>,
    pub payer_tax: Option<rust_decimal::Decimal>,
    pub payer_tax_currency: Option<String>,
    pub remark: Option<String>,
}

pub struct FileParser;

impl FileParser {
    pub fn parse_file(file_path: &Path) -> AppResult<ParseResult> {
        let extension = file_path
            .extension()
            .and_then(|ext| ext.to_str())
            .ok_or_else(|| AppError::InvalidFormat("No file extension found".to_string()))?;

        match extension.to_lowercase().as_str() {
            "csv" => Self::parse_csv(file_path),
            "xlsx" | "xls" => Self::parse_excel(file_path),
            _ => Err(AppError::InvalidFormat(format!(
                "Unsupported file format: {}",
                extension
            ))),
        }
    }

    /// 快速获取文件行数，不解析具体内容，适用于大文件的行数统计
    pub fn get_row_count_fast(file_path: &Path) -> AppResult<usize> {
        let extension = file_path
            .extension()
            .and_then(|ext| ext.to_str())
            .ok_or_else(|| AppError::InvalidFormat("No file extension found".to_string()))?;

        match extension.to_lowercase().as_str() {
            "csv" => Self::get_csv_row_count_fast(file_path),
            "xlsx" | "xls" => Self::get_excel_row_count_fast(file_path),
            _ => Err(AppError::InvalidFormat(format!(
                "Unsupported file format: {}",
                extension
            ))),
        }
    }

    fn parse_csv(file_path: &Path) -> AppResult<ParseResult> {
        let mut reader = ReaderBuilder::new()
            .has_headers(true)
            .from_path(file_path)?;

        let headers: Vec<String> = reader.headers()?.iter().map(|h| h.to_string()).collect();

        let mut rows = Vec::new();
        let mut row_number = 1; // Start from 1 (excluding header)

        for result in reader.records() {
            let record = result?;
            let mut data = HashMap::new();

            for (i, field) in record.iter().enumerate() {
                if let Some(header) = headers.get(i) {
                    // Try to parse as number first, then as string
                    let value = if let Ok(num) = field.parse::<f64>() {
                        Value::Number(
                            serde_json::Number::from_f64(num)
                                .unwrap_or_else(|| serde_json::Number::from_f64(0.0).unwrap()),
                        )
                    } else if field.is_empty() {
                        Value::Null
                    } else {
                        Value::String(field.to_string())
                    };

                    data.insert(header.clone(), value);
                }
            }

            rows.push(ParsedRow { row_number, data });
            row_number += 1;
        }

        Ok(ParseResult {
            total_rows: rows.len(),
            rows,
            headers,
        })
    }

    fn parse_excel(file_path: &Path) -> AppResult<ParseResult> {
        let mut workbook: Xlsx<_> =
            open_workbook(file_path).map_err(|e| AppError::Excel(calamine::Error::Xlsx(e)))?;

        // Get the first worksheet
        let worksheet_name = workbook
            .sheet_names()
            .first()
            .ok_or_else(|| AppError::InvalidFormat("No worksheets found".to_string()))?
            .clone();

        let range = workbook
            .worksheet_range(&worksheet_name)
            .map_err(|e| AppError::Excel(calamine::Error::Xlsx(e)))?;

        let mut rows = Vec::new();
        let mut headers = Vec::new();
        let mut is_first_row = true;

        for (row_idx, row) in range.rows().enumerate() {
            if is_first_row {
                // Extract headers from first row
                headers = row
                    .iter()
                    .map(|cell| cell.to_string().trim().to_string())
                    .collect();
                is_first_row = false;
                continue;
            }

            let mut data = HashMap::new();

            for (col_idx, cell) in row.iter().enumerate() {
                if let Some(header) = headers.get(col_idx) {
                    if header.is_empty() {
                        continue;
                    }

                    let value = match cell {
                        Data::Empty => Value::Null,
                        Data::String(s) => Value::String(s.clone()),
                        Data::Float(f) => Value::Number(
                            serde_json::Number::from_f64(*f)
                                .unwrap_or_else(|| serde_json::Number::from_f64(0.0).unwrap()),
                        ),
                        Data::Int(i) => Value::Number((*i).into()),
                        Data::Bool(b) => Value::Bool(*b),
                        Data::DateTime(dt) => Value::String(dt.to_string()),
                        Data::DateTimeIso(dt) => Value::String(dt.clone()),
                        Data::DurationIso(d) => Value::String(d.clone()),
                        Data::Error(e) => Value::String(format!("Error: {:?}", e)),
                    };

                    data.insert(header.clone(), value);
                }
            }

            rows.push(ParsedRow {
                row_number: row_idx, // row_idx starts from 0, but we skip header
                data,
            });
        }

        Ok(ParseResult {
            total_rows: rows.len(),
            rows,
            headers,
        })
    }

    pub fn parse_transaction_data(row_data: &HashMap<String, Value>) -> AppResult<TransactionData> {
        Ok(TransactionData {
            request_time: Self::parse_datetime(row_data.get("Request Time")),
            accounting_amount: Self::parse_decimal(row_data.get("Accounting Amount")),
            balance: Self::parse_decimal(row_data.get("Balance")),
            accounting_currency: Self::parse_string(row_data.get("Accounting Currency")),
            accounting_type: Self::parse_string(row_data.get("Accounting Type")),
            account_subject: Self::parse_string(row_data.get("Account Subject")),
            txn_create_time: Self::parse_datetime(row_data.get("Txn Create Time")),
            txn_complete_time: Self::parse_datetime(row_data.get("Txn complete Time")),
            merchant_txn_id: Self::parse_string(row_data.get("Merchant Txn ID")),
            trade_order_id: Self::parse_string(row_data.get("Trade Order ID")),
            country: Self::parse_string(row_data.get("Country")),
            txn_amount: Self::parse_decimal(row_data.get("Txn Amount")),
            txn_currency: Self::parse_string(row_data.get("Txn Currency")),
            payee_txn_fee: Self::parse_decimal(row_data.get("Payee Txn Fee")),
            payee_txn_fee_currency: Self::parse_string(row_data.get("Payee Txn Fee Currency")),
            payer_txn_fee: Self::parse_decimal(row_data.get("Payer Txn Fee")),
            payer_txn_fee_currency: Self::parse_string(row_data.get("Payer Txn Fee Currency")),
            payee_tax: Self::parse_decimal(row_data.get("Payee Tax")),
            payee_tax_currency: Self::parse_string(row_data.get("Payee Tax Currency")),
            payer_tax: Self::parse_decimal(row_data.get("Payer Tax")),
            payer_tax_currency: Self::parse_string(row_data.get("Payer Tax Currency")),
            remark: Self::parse_string(row_data.get("Remark")),
        })
    }

    fn parse_string(value: Option<&Value>) -> Option<String> {
        value.and_then(|v| match v {
            Value::String(s) => {
                let trimmed = s.trim();
                if trimmed.is_empty() {
                    None
                } else {
                    Some(trimmed.to_string())
                }
            }
            Value::Number(n) => Some(n.to_string()),
            _ => None,
        })
    }

    fn parse_decimal(value: Option<&Value>) -> Option<rust_decimal::Decimal> {
        value.and_then(|v| match v {
            Value::Number(n) => n
                .as_f64()
                .and_then(|f| rust_decimal::Decimal::from_f64_retain(f)),
            Value::String(s) => {
                let trimmed = s.trim();
                if trimmed.is_empty() {
                    None
                } else {
                    trimmed.parse::<rust_decimal::Decimal>().ok()
                }
            }
            _ => None,
        })
    }

    fn parse_datetime(value: Option<&Value>) -> Option<DateTime<Utc>> {
        value.and_then(|v| match v {
            Value::String(s) => {
                let trimmed = s.trim();
                if trimmed.is_empty() {
                    return None;
                }

                // Try parsing different datetime formats
                // Format: "2022-07-01 00:00:16"
                if let Ok(naive_dt) = NaiveDateTime::parse_from_str(trimmed, "%Y-%m-%d %H:%M:%S") {
                    return Some(DateTime::from_naive_utc_and_offset(naive_dt, Utc));
                }

                // Add more datetime formats as needed
                None
            }
            _ => None,
        })
    }

    /// 快速获取CSV文件行数（不解析内容）
    fn get_csv_row_count_fast(file_path: &Path) -> AppResult<usize> {
        let mut reader = ReaderBuilder::new()
            .has_headers(true)
            .from_path(file_path)?;

        let mut count = 0;
        for result in reader.records() {
            result?; // 确保记录有效，但不存储内容
            count += 1;
        }

        Ok(count)
    }

    /// 快速获取Excel文件行数（不解析内容，只获取维度信息）
    fn get_excel_row_count_fast(file_path: &Path) -> AppResult<usize> {
        use calamine::{open_workbook_auto, Reader};

        // 使用 open_workbook_auto 自动检测格式，并且只读取维度信息
        let mut workbook = open_workbook_auto(file_path)
            .map_err(|e| AppError::InvalidFormat(format!("Failed to open workbook: {}", e)))?;

        // Get the first worksheet
        let worksheet_names = workbook.sheet_names().to_owned();
        let worksheet_name = worksheet_names
            .first()
            .ok_or_else(|| AppError::InvalidFormat("No worksheets found".to_string()))?;

        // 只获取工作表的维度信息，不读取实际数据
        if let Some(range) = workbook.worksheet_range(worksheet_name).ok() {
            let total_rows = range.height();
            if total_rows > 0 {
                Ok(total_rows - 1) // 减去表头行
            } else {
                Ok(0)
            }
        } else {
            Ok(0)
        }
    }

    /// 超快速获取Excel文件行数（使用流式读取，适用于超大文件）
    pub fn get_row_count_streaming(file_path: &Path) -> AppResult<usize> {
        let extension = file_path
            .extension()
            .and_then(|ext| ext.to_str())
            .ok_or_else(|| AppError::InvalidFormat("No file extension found".to_string()))?;

        match extension.to_lowercase().as_str() {
            "csv" => Self::get_csv_row_count_streaming(file_path),
            "xlsx" | "xls" => Self::get_excel_row_count_streaming(file_path),
            _ => Err(AppError::InvalidFormat(format!(
                "Unsupported file format: {}",
                extension
            ))),
        }
    }

    /// 流式读取CSV文件行数
    fn get_csv_row_count_streaming(file_path: &Path) -> AppResult<usize> {
        use std::fs::File;
        use std::io::{BufRead, BufReader};

        let file = File::open(file_path)?;
        let reader = BufReader::new(file);

        let mut count = 0;
        let mut is_first_line = true;

        for line in reader.lines() {
            line?; // 确保行有效
            if is_first_line {
                is_first_line = false; // 跳过表头
                continue;
            }
            count += 1;
        }

        Ok(count)
    }

    /// 流式读取Excel文件行数（使用最小内存占用）
    fn get_excel_row_count_streaming(file_path: &Path) -> AppResult<usize> {
        // 对于Excel文件，我们仍然需要使用calamine，但可以优化读取方式
        // 这里我们使用一个更轻量级的方法
        Self::get_excel_row_count_fast(file_path)
    }
}
