[workspace]
members = [".", "entity", "migration"]
resolver = "2"

[package]
name = "payermax"
version = "0.1.0"
edition = "2021"

# Main application
[[bin]]
name = "payermax"
path = "src/main.rs"

# Core batch import tools
[[bin]]
name = "batch_import"
path = "src/bin/batch_import.rs"

[[bin]]
name = "import_single_file"
path = "src/bin/import_single_file.rs"

# Data verification and analysis tools
[[bin]]
name = "check_data"
path = "src/bin/check_data.rs"

[[bin]]
name = "quick_check_data"
path = "src/bin/quick_check_data.rs"

[[bin]]
name = "check_excel_columns"
path = "src/bin/check_excel_columns.rs"

# Database management tools
[[bin]]
name = "clean_partitions"
path = "src/bin/clean_partitions.rs"

[[bin]]
name = "list_partitions"
path = "src/bin/list_partitions.rs"

[[bin]]
name = "fix_import_records"
path = "src/bin/fix_import_records.rs"

# Statistics and reporting tools
[[bin]]
name = "generate_sum_transactions"
path = "src/bin/generate_sum_transactions.rs"

[[bin]]
name = "query_sum_transactions"
path = "src/bin/query_sum_transactions.rs"

[[bin]]
name = "generate_single_file_summary"
path = "src/bin/generate_single_file_summary.rs"

# Development and testing tools
[[bin]]
name = "test_partition"
path = "src/bin/test_partition.rs"

[[bin]]
name = "test_timing"
path = "src/bin/test_timing.rs"

# File conversion tools
[[bin]]
name = "excel_to_csv"
path = "src/bin/excel_to_csv.rs"

# Data query tools
[[bin]]
name = "query_sum_data"
path = "src/bin/query_sum_data.rs"

# CSV import tools
[[bin]]
name = "csv_batch_import"
path = "src/bin/csv_batch_import.rs"

[[bin]]
name = "csv_import_single_file"
path = "src/bin/csv_import_single_file.rs"


[dependencies]
# Local crates
entity = { path = "entity" }
migration = { path = "migration" }

# Web framework
salvo = { version = "0.82", features = [
    "serve-static",
    "cors",
    "compression",
    "websocket",
    "oapi",
] }
tokio = { version = "1.0", features = ["full"] }

# Database ORM
sea-orm = { version = "1.1.14", features = [
    "sqlx-postgres",
    "runtime-tokio-rustls",
    "macros",
    "with-chrono",
    "with-rust_decimal",
] }
sqlx = "0.8.6"

# CSV/Excel processing
csv = "1.3"
calamine = "0.30.0"

# Data types
rust_decimal = { version = "1.32", features = ["serde"] }
chrono = { version = "0.4", features = ["serde"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Error handling and utilities
anyhow = "1.0"
thiserror = "2.0"
uuid = { version = "1.0", features = ["v4", "serde"] }

# Logging
tracing = "0.1"
tracing-subscriber = "0.3"

# Environment variables
dotenvy = "0.15"
