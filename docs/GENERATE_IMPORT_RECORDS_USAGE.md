# Import Record 生成工具使用说明

## 概述

`generate_import_records` 是一个专门用于生成和管理 `import_record` 表记录的工具。它可以扫描指定文件夹中的Excel/CSV文件，与数据库中的交易记录进行对比，并自动创建相应的导入记录。

## 功能特性

- 🔍 **智能扫描**：自动扫描指定目录中的Excel/CSV文件
- 📊 **数据对比**：对比Excel文件行数与数据库记录数
- 🚀 **快速解析**：使用优化的Excel行数统计方法
- 📋 **重复检测**：自动跳过已存在的import_record记录
- 🔄 **强制更新**：支持强制覆盖现有记录
- 📈 **状态管理**：根据数据对比结果设置合适的状态
- 📊 **详细统计**：提供完整的处理结果统计

## 使用方法

### 基本语法

```bash
cargo run --bin generate_import_records [选项] [目录]
```

### 命令行选项

| 选项 | 简写 | 描述 |
|------|------|------|
| `--update` | `-u` | 更新模式（跳过已存在的记录）- 默认模式 |
| `--force` | `-f` | 强制更新所有记录（删除现有记录后重新创建） |
| `--help` | `-h` | 显示帮助信息 |

### 参数

- `目录`：要扫描的文件夹路径（默认: `data`）

## 使用示例

### 1. 默认模式（更新模式）

```bash
# 扫描默认data目录，跳过已存在的记录
cargo run --bin generate_import_records

# 扫描指定目录，跳过已存在的记录
cargo run --bin generate_import_records /path/to/excel/files
```

### 2. 强制更新模式

```bash
# 强制更新data目录中所有文件的记录
cargo run --bin generate_import_records --force

# 强制更新指定目录中所有文件的记录
cargo run --bin generate_import_records --force /path/to/excel/files
```

### 3. 显式指定更新模式

```bash
# 明确指定更新模式
cargo run --bin generate_import_records --update data
```

## 工作流程

### 1. 目录扫描阶段
- 扫描指定目录中的所有 `.xlsx`、`.xls`、`.csv` 文件
- 查询数据库确认每个文件是否有对应的交易记录
- 过滤掉没有数据库记录的文件

### 2. 记录检查阶段
- 检查每个文件是否已存在 `import_record` 记录
- 根据模式决定是跳过还是覆盖现有记录

### 3. 数据对比阶段
- 使用快速方法获取Excel文件的行数
- 与数据库中对应文件的记录数进行对比
- 记录数据差异情况

### 4. 记录创建阶段
- 根据对比结果创建 `import_record` 记录
- 设置合适的状态和统计信息

## 状态说明

工具会根据数据对比结果设置不同的状态：

| 状态 | 描述 | 条件 |
|------|------|------|
| `completed` | 导入完成 | Excel行数 = 数据库记录数 |
| `partial` | 部分导入 | Excel行数 > 数据库记录数 |
| `completed` | 导入完成 | Excel行数 < 数据库记录数（认为是正常情况） |

## 输出示例

### 更新模式输出
```
📝 默认使用更新模式（跳过已存在记录）
=== Import Record 生成工具 ===
📁 扫描目录: data
🔧 模式: 更新模式
✅ 数据库连接成功
🔍 扫描目录: data
📁 发现 170 个Excel/CSV文件
✅ 找到 170 个有数据库记录的文件

=== 开始处理文件 ===
📋 file1.xlsx - 已存在记录，跳过
🔍 file2.xlsx - 正在获取Excel行数...
📄 file2.xlsx - Excel行数: 200000 (耗时: 49.27s)
✅ file2.xlsx - 数据一致
✅ file2.xlsx - 成功创建import_record

=== 处理结果统计 ===
📊 总文件数: 170
✅ 新创建记录: 5
📋 已存在记录: 165
❌ 创建失败: 0
⏭️  跳过文件: 0
🎉 成功为 5 个文件创建了 import_record 记录！
```

### 强制更新模式输出
```
🔄 启用强制更新模式
=== Import Record 生成工具 ===
📁 扫描目录: test_data
🔧 模式: 强制更新
✅ 数据库连接成功

=== 开始处理文件 ===
🔄 file1.xlsx - 强制更新模式，将覆盖现有记录
🔍 file1.xlsx - 正在获取Excel行数...
📄 file1.xlsx - Excel行数: 199995 (耗时: 56.00s)
✅ file1.xlsx - 数据一致
🗑️  file1.xlsx - 删除了 1 条现有记录
✅ file1.xlsx - 成功创建import_record
```

## 注意事项

1. **数据库连接**：确保 `.env` 文件中的数据库配置正确
2. **文件权限**：确保对指定目录有读取权限
3. **磁盘空间**：大文件解析可能需要一定的临时空间
4. **处理时间**：大文件（20MB+）的Excel解析可能需要40-60秒
5. **并发限制**：工具是串行处理文件，避免过度占用系统资源

## 错误处理

工具会处理以下常见错误：
- 目录不存在
- 文件读取权限问题
- 数据库连接失败
- Excel文件格式错误
- 数据库操作失败

所有错误都会有详细的错误信息输出，便于问题排查。

## 性能优化

- 使用快速Excel行数统计方法，避免完整解析
- 智能跳过已存在的记录，减少重复工作
- 提供详细的进度信息和耗时统计
- 支持指定特定目录，避免扫描不必要的文件

## 与其他工具的关系

- `check_data.rs`：数据检查和对比工具
- `fast_check_data.rs`：带缓存的快速检查工具
- `generate_import_records.rs`：专门的import_record生成工具

这些工具可以配合使用，形成完整的数据管理工作流。
