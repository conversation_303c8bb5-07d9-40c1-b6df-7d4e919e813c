# 新工具使用说明

本文档介绍两个新增的工具：Excel 转 CSV 工具和 sum_transactions 查询工具。

## 1. Excel 转 CSV 工具 (excel_to_csv)

### 功能描述

将 Excel 文件（.xlsx, .xls）转换为 CSV 格式，支持单文件转换和批量转换。

### 使用方法

#### 基本语法

```bash
cargo run --bin excel_to_csv [选项]
```

#### 命令行选项

- `-d <目录>` - 指定输入目录，批量转换所有 Excel 文件
- `-f <文件>` - 指定单个 Excel 文件进行转换
- `-o <目录>` - 指定输出目录（可选，默认为当前目录）

#### 使用示例

1. **单文件转换**

```bash
# 转换单个文件到指定目录
cargo run --bin excel_to_csv -- -f data/file.xlsx -o output

# 转换单个文件到当前目录
cargo run --bin excel_to_csv -- -f data/file.xlsx
```

2. **批量转换**

```bash
# 批量转换目录中所有Excel文件到指定目录
cargo run --bin excel_to_csv -- -d data -o output

# 批量转换到当前目录
cargo run --bin excel_to_csv -- -d data
```

3. **查看详细日志**

```bash
RUST_LOG=info cargo run --bin excel_to_csv -- -f data/file.xlsx -o output
```

#### 功能特性

- 支持 .xlsx 和 .xls 格式
- 自动处理多个工作表（如果 Excel 文件有多个工作表，会生成多个 CSV 文件）
- 自动创建输出目录
- 详细的转换进度日志
- 错误处理和统计

## 2. sum_transactions 查询工具 (query_sum_data)

### 功能描述

查询 sum_transactions 表中的数据并导出为 CSV 格式，支持全量查询和单字段条件查询。

### 使用方法

#### 基本语法

```bash
cargo run --bin query_sum_data [选项]
```

#### 命令行选项

- `-f` - 全量查询所有数据
- `-q <field=value>` - 单字段查询，格式: field=value
- `-o <目录>` - 指定输出目录（可选，默认为当前目录）

#### 支持的查询字段

- `file` - 文件名
- `request_time` - 请求时间
- `account_subject` - 账户主题
- `account_type` - 账户类型
- `account_currency` - 账户货币

#### 使用示例

1. **全量查询**

```bash
# 全量查询并保存到指定目录
cargo run --bin query_sum_data -- -f -o output

# 全量查询到当前目录
cargo run --bin query_sum_data -- -f
```

2. **单字段查询**

```bash
# 按文件名查询
cargo run --bin query_sum_data -- -q file=SP20519158_bill_20220701_20230630_0.xlsx

# 按货币查询
cargo run --bin query_sum_data -- -q account_currency=USD -o results

# 按账户类型查询
cargo run --bin query_sum_data -- -q account_type=Settlement

# 按时间查询
cargo run --bin query_sum_data -- -q request_time=202208
```

3. **查看详细日志**

```bash
RUST_LOG=info cargo run --bin query_sum_data -- -f -o output
```

#### 输出文件命名规则

- 全量查询: `sum_transactions_full.csv`
- 单字段查询: `sum_transactions_{字段名}_{值}.csv`

#### 功能特性

- 支持全量数据导出
- 支持多种字段的条件查询
- 自动生成 CSV 文件头
- 详细的查询统计信息
- 自动创建输出目录
- 数据库连接状态显示

## 配置要求

### 环境变量

确保已正确配置数据库连接环境变量：

```env
DATABASE_URL=postgres://username:password@localhost:5432/payermax
```

### 依赖项

这两个工具使用了以下主要依赖：

- `calamine` - Excel 文件读取
- `csv` - CSV 文件写入
- `sea-orm` - 数据库 ORM
- `tracing` - 日志记录

## 注意事项

1. **Excel 转 CSV 工具**

   - 大文件转换可能需要较长时间
   - 确保有足够的磁盘空间存储转换后的 CSV 文件
   - 如果 Excel 文件有多个工作表，会为每个工作表生成单独的 CSV 文件

2. **查询工具**

   - 全量查询可能返回大量数据，请确保有足够的磁盘空间
   - 查询时间取决于数据库中的记录数量
   - 确保数据库连接正常

3. **通用注意事项**
   - 使用 `RUST_LOG=info` 可以查看详细的执行日志
   - 输出目录会自动创建，无需手动创建
   - 所有工具都支持相对路径和绝对路径

## 3. CSV 批量导入工具 (csv_batch_import)

### 功能描述

批量导入 CSV 文件到数据库，基于现有的 BatchImportService，支持高并发处理和自动去重。

### 使用方法

#### 基本语法

```bash
cargo run --bin csv_batch_import <csv_directory> [max_concurrent_files] [batch_size]
```

#### 参数说明

- `csv_directory` - 包含 CSV 文件的目录路径
- `max_concurrent_files` - 最大并发文件数（可选，默认 4）
- `batch_size` - 批处理大小（可选，默认 1000）

#### 使用示例

1. **基本批量导入**

```bash
# 使用默认参数导入
cargo run --bin csv_batch_import csv_data

# 指定并发数和批处理大小
cargo run --bin csv_batch_import csv_data 4 1000
```

2. **查看详细日志**

```bash
RUST_LOG=info cargo run --bin csv_batch_import csv_data 2 500
```

#### 功能特性

- 支持高并发文件处理
- 自动创建数据库分区
- 智能去重（跳过已导入的记录）
- 详细的进度报告
- 连接池优化

## 4. CSV 单文件导入工具 (csv_import_single_file)

### 功能描述

导入单个 CSV 文件到数据库，提供详细的导入统计和验证功能。

### 使用方法

#### 基本语法

```bash
cargo run --bin csv_import_single_file <csv_file_path> [batch_size]
```

#### 参数说明

- `csv_file_path` - CSV 文件路径
- `batch_size` - 批处理大小（可选，默认 1000）

#### 使用示例

1. **单文件导入**

```bash
# 基本导入
cargo run --bin csv_import_single_file data/transactions.csv

# 指定批处理大小
cargo run --bin csv_import_single_file data/transactions.csv 500
```

2. **查看详细日志**

```bash
RUST_LOG=info cargo run --bin csv_import_single_file data/transactions.csv
```

#### 功能特性

- 导入前后数据验证
- 详细的 CSV 列信息显示
- 重复导入检测
- 导入进度跟踪
- 错误诊断建议

## 配置要求

### 环境变量

确保已正确配置数据库连接环境变量：

```env
DATABASE_URL=postgres://username:password@localhost:5432/payermax
```

### 依赖项

这些工具使用了以下主要依赖：

- `calamine` - Excel 文件读取
- `csv` - CSV 文件读写
- `sea-orm` - 数据库 ORM
- `tracing` - 日志记录

## 注意事项

1. **Excel 转 CSV 工具**

   - 大文件转换可能需要较长时间
   - 确保有足够的磁盘空间存储转换后的 CSV 文件
   - 如果 Excel 文件有多个工作表，会为每个工作表生成单独的 CSV 文件

2. **查询工具**

   - 全量查询可能返回大量数据，请确保有足够的磁盘空间
   - 查询时间取决于数据库中的记录数量
   - 确保数据库连接正常

3. **CSV 导入工具**

   - 大文件导入可能需要较长时间，请耐心等待
   - 批量导入支持断点续传，可以安全中断和重新开始
   - 确保 CSV 文件格式与数据库表结构匹配
   - 导入过程中会自动创建数据库分区以提高性能

4. **通用注意事项**
   - 使用 `RUST_LOG=info` 可以查看详细的执行日志
   - 输出目录会自动创建，无需手动创建
   - 所有工具都支持相对路径和绝对路径

## 故障排除

1. **编译错误**

   ```bash
   cargo build --bin excel_to_csv --bin query_sum_data --bin csv_batch_import --bin csv_import_single_file
   ```

2. **数据库连接错误**

   - 检查 `.env` 文件中的 `DATABASE_URL` 配置
   - 确保 PostgreSQL 服务正在运行
   - 验证数据库用户权限

3. **CSV 导入错误**

   - 检查 CSV 文件格式和编码（推荐 UTF-8）
   - 确保 CSV 列名与预期的字段名匹配
   - 验证数据类型兼容性
   - 检查是否有必需字段缺失

4. **文件权限错误**

   - 确保对输入文件有读取权限
   - 确保对输出目录有写入权限

5. **内存不足**

   - 对于大文件，考虑分批处理
   - 监控系统内存使用情况
   - 调整批处理大小参数

6. **性能优化**
   - 调整并发文件数以匹配系统性能
   - 根据内存情况调整批处理大小
   - 监控数据库连接池使用情况
